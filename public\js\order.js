// 訂單處理相關功能

// 全局訂單變量 - 確保在 window 對象上可訪問
window.currentOrder = {
  items: [],
  totalAmount: 0,
  id: null
};

// 為了向後兼容，也保留本地變量
let currentOrder = window.currentOrder;

// 在這裡添加與訂單相關的JavaScript代碼

// 確保訂單完成提示窗口能夠正確顯示
document.addEventListener('DOMContentLoaded', function() {
    // 監聽結帳按鈕點擊事件
    document.addEventListener('click', function(event) {
        if (event.target.classList.contains('checkout-btn') || 
            (event.target.parentElement && event.target.parentElement.classList.contains('checkout-btn'))) {
            // 顯示訂單完成提示窗口
            const modal = document.getElementById('order-confirmation-modal');
            if (modal) {
                modal.style.display = 'flex';
                console.log('訂單完成提示窗口已顯示 (從order.js)');
            } else {
                console.error('找不到訂單完成提示窗口元素 (從order.js)');
            }
        }
    });
});

// 關閉訂單完成提示窗口
window.closeOrderConfirmationModal = function() {
    const modal = document.getElementById('order-confirmation-modal');
    if (modal) {
        modal.style.display = 'none';
    }
};

// 開始新訂單
window.startNewOrder = function() {
    closeOrderConfirmationModal();
    // 清空訂單輸入框
    const orderInput = document.getElementById('order-input');
    if (orderInput) {
        orderInput.value = '';
    }
    // 清空訂單結果容器
    const orderResultContainer = document.getElementById('order-result-container');
    if (orderResultContainer) {
        orderResultContainer.innerHTML = '';
    }
    // 顯示提示訊息
    if (window.showToastMessage) {
        window.showToastMessage('已開始新的訂單', 'info');
    }
};

// 確保刪除任何可能的測試數據
(function clearTestData() {
  // 重設訂單變量確保乾淨的狀態
  currentOrder = {
    items: [],
    totalAmount: 0,
    id: null
  };
  console.log('訂單系統已初始化，確保無測試數據');
})();

// 初始化訂單頁面
function initOrderSystem() {
  // 重設訂單物件確保每次都是乾淨狀態
  currentOrder = {
    items: [],
    totalAmount: 0,
    id: null
  };
  
  console.log('訂單系統初始化，currentOrder 已重置為空狀態');
  
  // 載入訂單相關事件監聽器
  document.addEventListener('DOMContentLoaded', () => {
    bindOrderEvents();
  });
}

// 綁定訂單相關事件
function bindOrderEvents() {
  // 確認訂單按鈕
  const confirmOrderBtn = document.getElementById('confirm-order-btn');
  if (confirmOrderBtn) {
    confirmOrderBtn.addEventListener('click', showOrderConfirmation);
  }
}

// 顯示訂單確認對話框
function showOrderConfirmation() {
  // 獲取當前訂單商品
  const orderItems = currentOrder.items;
  
  if (!orderItems || orderItems.length === 0) {
    showNotification(getTranslation('select_items_first') || '請先選擇商品加入購物車', 'error');
    return;
  }
  
  // 構建訂單摘要HTML
  let orderSummaryHTML = '<div class="order-confirmation">';
  orderSummaryHTML += '<h3>訂單確認</h3>';
  orderSummaryHTML += '<p>AI為您總結了訂單，請確認：</p>';
  orderSummaryHTML += '<div class="order-summary">';
  
  // 英文訂單摘要（頁面所示）
  const itemsText = orderItems.map(item => `${item.quantity} x ${item.name} at $${item.price}`).join('. ');
  orderSummaryHTML += `<div class="en-summary">${itemsText}. Please proceed to checkout to finalize your purchase. We appreciate your business!</div>`;
  
  orderSummaryHTML += '</div>';
  orderSummaryHTML += '<div class="order-actions">';
  orderSummaryHTML += '<button id="confirm-correct-btn" class="btn btn-primary">是的，訂單正確</button>';
  orderSummaryHTML += '<button id="cancel-order-btn" class="btn btn-secondary">不對，重新點餐</button>';
  orderSummaryHTML += '</div></div>';
  
  // 顯示確認對話框
  const dialogContainer = document.createElement('div');
  dialogContainer.className = 'dialog-container';
  dialogContainer.innerHTML = orderSummaryHTML;
  document.body.appendChild(dialogContainer);
  
  // 綁定確認和取消按鈕事件
  document.getElementById('confirm-correct-btn').addEventListener('click', () => {
    processOrder();
    document.body.removeChild(dialogContainer);
  });
  
  document.getElementById('cancel-order-btn').addEventListener('click', () => {
    document.body.removeChild(dialogContainer);
  });
}

// 處理訂單提交
async function processOrder() {
  try {
    // 記錄訂單提交開始
    console.log('開始提交訂單:', {
      orderItems: currentOrder.items,
      totalAmount: currentOrder.totalAmount
    });
      // 過濾訂單中的有效項目，確保每個項目都有必要的屬性
    const validItems = currentOrder.items.filter(item => 
      item && 
      item.name && 
      typeof item.name === 'string' &&
      // 排除純數字名稱（通常是測試數據）
      !/^\d+$/.test(item.name.trim()) &&
      typeof item.price === 'number' && 
      typeof item.quantity === 'number' && 
      item.quantity > 0
    );
    
    console.log('過濾後有效項目數:', validItems.length, '原始項目數:', currentOrder.items.length);
    
    // 構建訂單數據，確保不包含 undefined 值
    const orderData = {
      items: validItems
    };
      // 只有在 userId 有有效值(非 null/undefined/空字串)時才添加
    if (currentOrder.userId && typeof currentOrder.userId === 'string' && currentOrder.userId.trim() !== '') {
      orderData.userId = currentOrder.userId.trim();
      console.log('添加有效的用戶ID:', orderData.userId);
    } else {
      console.log('無用戶ID或無效ID，提交匿名訂單');
    }

    console.log('準備提交訂單數據:', orderData);
    
    // 向後端提交訂單
    const response = await fetch('/api/order', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(orderData),
    });

    // 記錄伺服器回應狀態
    console.log('伺服器回應狀態:', {
      status: response.status,
      statusText: response.statusText
    });

    const orderResult = await response.json();
    
    if (response.ok) {
      currentOrder.id = orderResult.id;
      const successDetails = {
        orderId: orderResult.id,
        status: 'success',
        timestamp: new Date().toISOString()
      };
      console.log('訂單提交成功:', successDetails);
      showOrderStatusNotification(getTranslation('order_submitted') || '訂單提交成功！', 'success', successDetails);
      showOrderCompletionDialog();
    } else {
      // 記錄詳細的錯誤信息
      const errorDetails = {
        status: response.status,
        statusText: response.statusText,
        error: orderResult.error,
        orderData: currentOrder,
        timestamp: new Date().toISOString()
      };
      console.error('訂單提交失敗:', errorDetails);
      throw new Error(orderResult.error || `訂單提交失敗 (狀態碼: ${response.status})`);
    }
  } catch (error) {
    // 區分網絡錯誤和伺服器錯誤
    const isNetworkError = error instanceof TypeError;
    const errorMessage = isNetworkError ? 
      '網絡連接錯誤，請檢查您的網絡連接' : 
      `訂單處理失敗: ${error.message}`;

    const errorDetails = {
      error: error.message,
      errorType: error.constructor.name,
      isNetworkError: isNetworkError,
      orderData: currentOrder,
      timestamp: new Date().toISOString()
    };

    console.error('訂單處理出錯:', errorDetails);
    showOrderStatusNotification(getTranslation('order_submit_failed') || errorMessage, 'error', errorDetails);
  }
}

// 顯示訂單完成對話框
function showOrderCompletionDialog() {
  const completionHTML = `
    <div class="order-success">
      <div class="success-icon">✓</div>
      <h2>訂單完成！</h2>
      <p>感謝您的訂購！您的餐點正在準備中。</p>
      <div class="order-details">
        <p>訂單摘要:</p>
        <ul>
          ${currentOrder.items.map(item => `<li>${item.name}(${item.quantity}) x ${item.quantity} - $${item.price * item.quantity}</li>`).join('')}
        </ul>
        <p>總金額: $${currentOrder.totalAmount.toFixed(2)}</p>
      </div>
      <button id="new-order-btn" class="btn btn-primary">點新的一單</button>
    </div>
  `;
  
  const completionDialog = document.createElement('div');
  completionDialog.className = 'dialog-container success-dialog';
  completionDialog.innerHTML = completionHTML;
  document.body.appendChild(completionDialog);
  
  document.getElementById('new-order-btn').addEventListener('click', () => {
    document.body.removeChild(completionDialog);
    resetOrder();
  });
  
  // 模擬訂單狀態更新
  setTimeout(() => {
    showOrderStatusNotification('訂單已送出！');
  }, 3000);
}

// 顯示訂單狀態通知
function showOrderStatusNotification(message, type = 'info', details = null) {
  const notificationElement = document.createElement('div');
  notificationElement.className = `status-notification ${type}`;

  // 根據通知類型選擇圖標
  const icons = {
    'success': '✓',
    'error': '❌',
    'warning': '⚠️',
    'info': 'ℹ️'
  };

  let notificationContent = `
    <div class="notification-content">
      <div class="notification-icon">${icons[type] || '🔔'}</div>
      <div class="notification-message">
        <div class="message-main">${message}</div>
  `;

  // 如果有詳細信息，添加展開按鈕和詳細信息區域
  if (details) {
    notificationContent += `
        <div class="message-details" style="display: none;">
          <pre>${JSON.stringify(details, null, 2)}</pre>
        </div>
        <button class="details-toggle">顯示詳細信息</button>
    `;
  }

  notificationContent += `
      </div>
    </div>
  `;

  notificationElement.innerHTML = notificationContent;
  document.body.appendChild(notificationElement);

  // 如果有詳細信息，綁定展開/收起事件
  if (details) {
    const toggleBtn = notificationElement.querySelector('.details-toggle');
    const detailsArea = notificationElement.querySelector('.message-details');
    
    toggleBtn.addEventListener('click', () => {
      const isHidden = detailsArea.style.display === 'none';
      detailsArea.style.display = isHidden ? 'block' : 'none';
      toggleBtn.textContent = isHidden ? '隱藏詳細信息' : '顯示詳細信息';
    });
  }

  // 添加動畫效果
  setTimeout(() => {
    notificationElement.classList.add('notification-slide-in');
  }, 100);

  // 設置自動關閉計時器（錯誤通知停留更久）
  const displayTime = type === 'error' ? 10000 : 5000;
  
  setTimeout(() => {
    notificationElement.classList.remove('notification-slide-in');
    notificationElement.classList.add('notification-slide-out');
    
    setTimeout(() => {
      if (document.body.contains(notificationElement)) {
        document.body.removeChild(notificationElement);
      }
    }, 500);
  }, displayTime);

  // 記錄通知到控制台
  console.log(`[${type.toUpperCase()}] ${message}`, details || '');
}

// 重設訂單
function resetOrder() {
  currentOrder = {
    items: [],
    totalAmount: 0,
    id: null
  };
  
  // 清空購物車UI
  const cartElement = document.getElementById('shopping-cart');
  if (cartElement) {
    cartElement.innerHTML = '<p>購物車是空的</p>';
  }
  
  // 更新總金額顯示
  updateTotalAmount();
}

// 更新總金額顯示
function updateTotalAmount() {
  const totalAmountElement = document.getElementById('total-amount');
  if (totalAmountElement) {
    totalAmountElement.textContent = `總金額: $${currentOrder.totalAmount.toFixed(2)}`;
  }
}

// 添加商品到購物車
function addItemToCart(item) {
  // 檢查商品是否已在購物車中
  const existingItem = currentOrder.items.find(i => i.id === item.id);
  
  if (existingItem) {
    existingItem.quantity += 1;
  } else {
    currentOrder.items.push({
      ...item,
      quantity: 1
    });
  }
  
  // 更新訂單總金額
  currentOrder.totalAmount = currentOrder.items.reduce(
    (sum, item) => sum + (item.price * item.quantity), 
    0
  );
  
  // 更新購物車UI
  updateCartUI();
}

// 更新購物車UI
function updateCartUI() {
  const cartElement = document.getElementById('shopping-cart');
  if (!cartElement) return;
  
  if (currentOrder.items.length === 0) {
    cartElement.innerHTML = '<p>購物車是空的</p>';
    return;
  }
  
  let cartHTML = '<ul class="cart-items">';
  
  currentOrder.items.forEach(item => {
    cartHTML += `
      <li class="cart-item">
        <div class="item-details">
          <span class="item-name">${item.name}</span>
          <span class="item-price">$${item.price}</span>
        </div>
        <div class="item-quantity">
          <button class="quantity-btn minus" data-id="${item.id}">-</button>
          <span>${item.quantity}</span>
          <button class="quantity-btn plus" data-id="${item.id}">+</button>
          <button class="remove-btn" data-id="${item.id}">🗑️</button>
        </div>
      </li>
    `;
  });
  
  cartHTML += '</ul>';
  cartElement.innerHTML = cartHTML;
  
  // 更新總金額
  updateTotalAmount();
  
  // 綁定數量按鈕事件
  document.querySelectorAll('.quantity-btn.minus').forEach(btn => {
    btn.addEventListener('click', (e) => {
      decreaseItemQuantity(e.target.dataset.id);
    });
  });
  
  document.querySelectorAll('.quantity-btn.plus').forEach(btn => {
    btn.addEventListener('click', (e) => {
      increaseItemQuantity(e.target.dataset.id);
    });
  });
  
  document.querySelectorAll('.remove-btn').forEach(btn => {
    btn.addEventListener('click', (e) => {
      removeItemFromCart(e.target.dataset.id);
    });
  });
}

// 通用通知函數
function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `notification ${type}`;
  notification.textContent = message;
  
  document.body.appendChild(notification);
  
  setTimeout(() => {
    notification.classList.add('show');
  }, 100);
  
  setTimeout(() => {
    notification.classList.remove('show');
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 500);
  }, 3000);
}

// order.js 結束
// 注意：初始化現在由 index.html 中的 DOMContentLoaded 事件處理
// 不要在這裡調用 initOrderSystem()，避免重複初始化
