import { Router, Request, Response } from 'express';
import { PromptEngine } from '../services/PromptEngine.js';
import { MenuData } from '../types/menu.js';
import * as fs from 'fs';
import * as path from 'path';

// 擴展 Request 類型以包含 promptEngine 屬性
interface ExtendedRequest extends Request {
  promptEngine?: PromptEngine;
}

const router = Router();
// 創建本地 promptEngine 作為後備
const localPromptEngine = new PromptEngine();

// 從請求中獲取 promptEngine 實例或使用本地實例
function getPromptEngine(req: ExtendedRequest): PromptEngine {
  return req.promptEngine || localPromptEngine;
}

// 定義處理器函數
/**
 * 驗證 BDD 語法
 */
const validateBDDHandler = (req: Request, res: Response) => {
  try {
    const { bddText } = req.body;
    
    if (!bddText) {
      return res.status(400).json({
        success: false,
        error: '請提供 BDD 文本'
      });
    }
    
    const promptEngine = getPromptEngine(req);
    const validation = promptEngine.validateBDDSyntax(bddText);
    
    res.json({
      success: true,
      validation
    });
  } catch (error) {
    console.error('BDD 驗證錯誤:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

/**
 * 驗證 AAprompt 語法
 */
const validateAAPromptHandler = (req: Request, res: Response) => {
  try {
    const { aaText } = req.body;
    
    if (!aaText) {
      return res.status(400).json({
        success: false,
        error: '請提供 AAprompt 文本'
      });
    }
    
    const promptEngine = getPromptEngine(req);
    const validation = promptEngine.validateAAPromptSyntax(aaText);
    
    res.json({
      success: true,
      validation
    });
  } catch (error) {
    console.error('AAprompt 驗證錯誤:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

/**
 * 從 BDD 生成 APPprompt
 */
const generateFromBDDHandler = async (req: Request, res: Response) => {
  try {
    const { bddText } = req.body;
    
    if (!bddText) {
      return res.status(400).json({
        success: false,
        error: '請提供 BDD 文本'
      });
    }
    
    const promptEngine = getPromptEngine(req);
    // 先驗證 BDD 語法
    const validation = promptEngine.validateBDDSyntax(bddText);
    
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        error: '無效的 BDD 語法',
        details: validation.errors
      });
    }
    
    if (!validation.spec) {
      return res.status(400).json({
        success: false,
        error: '無法生成有效的 BDD 規範'
      });
    }
    
    // 生成 APPprompt
    const appPrompt = await promptEngine.generateFromBDD(validation.spec);
    
    res.json({
      success: true,
      appPrompt
    });
  } catch (error) {
    console.error('APPprompt 生成錯誤:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

/**
 * 從 AAprompt 生成 APPprompt
 */
const generateFromAAPromptHandler = async (req: Request, res: Response) => {
  try {
    const { aaText } = req.body;
    
    if (!aaText) {
      return res.status(400).json({
        success: false,
        error: '請提供 AAprompt 文本'
      });
    }
    
    const promptEngine = getPromptEngine(req);
    // 先驗證 AAprompt 語法
    const validation = promptEngine.validateAAPromptSyntax(aaText);
    
    if (!validation.valid) {
      return res.status(400).json({
        success: false,
        error: '無效的 AAprompt 語法',
        details: validation.errors
      });
    }
    
    if (!validation.prompt) {
      return res.status(400).json({
        success: false,
        error: '無法生成有效的 AAprompt'
      });
    }
    
    // 生成 APPprompt
    const appPrompt = await promptEngine.generateFromAA(validation.prompt);
    
    res.json({
      success: true,
      appPrompt
    });
  } catch (error) {
    console.error('APPprompt 生成錯誤:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

/**
 * 從自然語言生成 APPprompt
 */
const generateFromNaturalHandler = async (req: Request, res: Response) => {
  try {
    const { text } = req.body;
    
    if (!text) {
      return res.status(400).json({
        success: false,
        error: '請提供自然語言文本'
      });
    }
      // 生成 APPprompt
    const promptEngine = getPromptEngine(req);
    const appPrompt = await promptEngine.generateFromNaturalLanguage(text);
    
    res.json({
      success: true,
      appPrompt
    });
  } catch (error) {
    console.error('APPprompt 生成錯誤:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

/**
 * 儲存 BDD 文本
 */
const saveBDDHandler = async (req: Request, res: Response) => {
  try {
    const { bddText } = req.body;
    
    if (!bddText) {
      return res.status(400).json({
        success: false,
        error: '請提供 BDD 文本'
      });
    }
    
    const promptEngine = getPromptEngine(req);
    // 先驗證 BDD 語法
    const validation = promptEngine.validateBDDSyntax(bddText);
    
    // 這裡可以添加儲存到資料庫或文件系統的邏輯
    // 目前只是驗證語法並回傳成功
    
    res.json({
      success: true,
      message: 'BDD 已成功儲存',
      isValid: validation.valid
    });
  } catch (error) {
    console.error('BDD 儲存錯誤:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

/**
 * 儲存 AAprompt 文本
 */
const saveAAPromptHandler = async (req: Request, res: Response) => {
  try {
    const { aaPromptText } = req.body;
    
    if (!aaPromptText) {
      return res.status(400).json({
        success: false,
        error: '請提供 AAprompt 文本'
      });
    }
    
    const promptEngine = getPromptEngine(req);
    // 先驗證 AAprompt 語法
    const validation = promptEngine.validateAAPromptSyntax(aaPromptText);
    
    // 這裡可以添加儲存到資料庫或文件系統的邏輯
    // 目前只是驗證語法並回傳成功
    
    res.json({
      success: true,
      message: 'AAprompt 已成功儲存',
      isValid: validation.valid
    });
  } catch (error) {
    console.error('AAprompt 儲存錯誤:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

/**
 * 處理客戶訂單 - 使用動態生成的 APPprompt
 */
const processOrderHandler = async (req: Request, res: Response) => {
  try {
    const { customerRequest, language } = req.body;
    
    if (!customerRequest) {
      return res.status(400).json({
        success: false,
        error: '請提供客戶訂單請求'
      });
    }
    
    const promptEngine = getPromptEngine(req);
    
    // 使用動態處理方法處理訂單，傳入語言參數
    const result = await promptEngine.processOrderWithDynamicPrompt(customerRequest, undefined, language || 'zh-TW');
    
    res.json({
      success: true,
      result
    });
  } catch (error) {
    console.error('動態訂單處理錯誤:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '處理訂單時發生錯誤'
    });
  }
};

/**
 * 獲取BDD預設內容
 */
const getBDDDefaultHandler = async (req: Request, res: Response) => {
  try {
    const { language } = req.query;

    // 根據語言確定文件名
    let fileName = 'BDD_TW.txt'; // 預設為繁體中文

    if (language === 'en-US') {
      fileName = 'BDD_En.txt';
    } else if (language === 'ja-JP') {
      fileName = 'BDD_JP.txt';
    }

    // 構建文件路徑
    const filePath = path.join(process.cwd(), fileName);

    // 檢查文件是否存在
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        error: `找不到語言文件: ${fileName}`
      });
    }

    // 讀取文件內容
    const content = fs.readFileSync(filePath, 'utf-8');

    res.json({
      success: true,
      content,
      language,
      fileName
    });
  } catch (error) {
    console.error('讀取BDD預設內容錯誤:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

/**
 * 統一的 APPprompt 生成端點，根據 type 決定使用哪個處理函數
 */
const generatePromptHandler = async (req: ExtendedRequest, res: Response) => {
  try {
    const { type, content, language } = req.body;
    
    if (!type || !content) {
      return res.status(400).json({
        success: false,
        error: '請提供類型 (type) 和內容 (content)'
      });
    }
    
    if (!['bdd', 'aaprompt', 'natural'].includes(type)) {
      return res.status(400).json({
        success: false,
        error: '無效的類型，支援的類型：bdd, aaprompt, natural'
      });
    }
    
    let appPrompt;
    const promptEngine = getPromptEngine(req);
    // 根據類型選擇處理邏輯
    if (type === 'bdd') {
      // 先驗證 BDD 語法
      const validation = promptEngine.validateBDDSyntax(content);
      
      if (!validation.valid) {
        return res.status(400).json({
          success: false,
          error: '無效的 BDD 語法',
          details: validation.errors
        });
      }
      
      if (!validation.spec) {
        return res.status(400).json({
          success: false,
          error: '無法生成有效的 BDD 規範'
        });
      }
      
      appPrompt = await promptEngine.generateFromBDD(validation.spec, language);
    } 
    else if (type === 'aaprompt') {
      // 先驗證 AAprompt 語法
      const validation = promptEngine.validateAAPromptSyntax(content);
      
      if (!validation.valid) {
        return res.status(400).json({
          success: false,
          error: '無效的 AAprompt 語法',
          details: validation.errors
        });
      }
      
      if (!validation.prompt) {
        return res.status(400).json({
          success: false,
          error: '無法生成有效的 AAprompt'
        });
      }
      
      appPrompt = await promptEngine.generateFromAA(validation.prompt, language);
    }
    else { // natural
      appPrompt = await promptEngine.generateFromNaturalLanguage(content);
    }
    
    // 顯示一些調試信息
    const menuData = promptEngine.getMenuData();
    console.log(`生成 APPprompt 時，${menuData ? '已使用菜單數據' : '沒有使用菜單數據'}`);    if (menuData) {
      console.log(`菜單包含 ${menuData.categories.reduce((sum, cat) => sum + cat.items.length, 0)} 個項目，來自餐廳: ${menuData.restaurant_name}`);
    }
    
    // 將生成的 APPprompt 保存到文件系統
    try {
      // 生成時間戳
      const timestamp = new Date().toISOString().replace(/[-:]/g, '').replace('T', '').substring(0, 12);
      const filename = `appPrompt_${timestamp}.json`;

      // 確保 appPrompt 目錄存在
      const appPromptDir = path.join(process.cwd(), 'appPrompt');
      if (!fs.existsSync(appPromptDir)) {
        console.log('創建 appPrompt 目錄');
        fs.mkdirSync(appPromptDir, { recursive: true });
      }

      // 將 APPprompt 寫入文件
      const filePath = path.join(appPromptDir, filename);
      fs.writeFileSync(filePath, JSON.stringify(appPrompt, null, 2));
      console.log(`已將 APPprompt 保存到文件: ${filePath}`);

      // 添加文件路徑到響應
      appPrompt.filePath = path.join('appPrompt', filename);
    } catch (saveError) {
      console.error('保存 APPprompt 文件時出錯:', saveError);
      // 繼續執行，不因保存失敗而中斷響應
    }
    
    res.json({
      success: true,
      appPrompt
    });
  } catch (error) {
    console.error('APPprompt 生成錯誤:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : '未知錯誤'
    });
  }
};

// 註冊路由
// 使用 Express 應用程式編程介面的正確型式
router.post('/validate-bdd', function(req, res) {
  validateBDDHandler(req, res);
});
router.post('/validate-aaprompt', function(req, res) {
  validateAAPromptHandler(req, res);
});
router.post('/generate-from-bdd', async function(req, res) {
  await generateFromBDDHandler(req, res);
});
router.post('/generate-from-aaprompt', async function(req, res) {
  await generateFromAAPromptHandler(req, res);
});
router.post('/generate-from-natural', async function(req, res) {
  await generateFromNaturalHandler(req, res);
});
router.post('/generate', async function(req, res) {
  await generatePromptHandler(req, res);
});
router.post('/save-bdd', async function(req, res) {
  await saveBDDHandler(req, res);
});
router.post('/save-aaprompt', async function(req, res) {
  await saveAAPromptHandler(req, res);
});
router.post('/process-order', async function(req, res) {
  await processOrderHandler(req, res);
});
router.get('/bdd-default', async function(req, res) {
  await getBDDDefaultHandler(req, res);
});

export default router;
